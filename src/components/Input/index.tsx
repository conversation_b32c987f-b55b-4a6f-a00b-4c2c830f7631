'use client'

import { forwardRef } from 'react'
import { InputPropsI } from './types'
import { CONFIG } from '@/constants/config'
import { cn } from '@/utils'

export const Input = forwardRef<HTMLInputElement, InputPropsI>(
  ({ 
    label,
    error,
    className,
    ...props 
  }, ref) => {
    return (
      <div className="w-full">
        {label && (
          <label htmlFor={props.id} className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <input
          ref={ref}
          className={cn(
            "appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
            error 
              ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
              : "border-gray-300 focus:border-transparent",
            `focus:ring-[${CONFIG.colors.primary}]`,
            className
          )}
          {...props}
        />
        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'
