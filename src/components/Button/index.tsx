'use client'

import { ButtonHTMLAttributes, forwardRef } from 'react'
import { ButtonPropsI } from './types'
import { CONFIG } from '@/constants/config'
import { cn } from '@/utils'

export const Button = forwardRef<HTMLButtonElement, ButtonPropsI>(
  ({ 
    variant = 'primary', 
    size = 'md', 
    children, 
    className, 
    disabled,
    ...props 
  }, ref) => {
    const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
    
    const variants = {
      primary: `text-white border border-transparent`,
      secondary: 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50',
      outline: `text-gray-700 bg-transparent border border-gray-300 hover:bg-gray-50`
    }
    
    const sizes = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    }

    const primaryStyles = {
      backgroundColor: disabled ? '#cccccc' : CONFIG.colors.primary,
      borderColor: disabled ? '#cccccc' : CONFIG.colors.primary,
      focusRingColor: CONFIG.colors.primary
    }

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        style={variant === 'primary' ? primaryStyles : undefined}
        disabled={disabled}
        onMouseEnter={variant === 'primary' && !disabled ? (e) => {
          e.currentTarget.style.backgroundColor = CONFIG.colors.primaryHover
        } : undefined}
        onMouseLeave={variant === 'primary' && !disabled ? (e) => {
          e.currentTarget.style.backgroundColor = CONFIG.colors.primary
        } : undefined}
        {...props}
      >
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'
