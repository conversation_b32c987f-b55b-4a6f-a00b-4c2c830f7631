'use client'

import { useState } from 'react'
import { useLoginForm } from './useLoginForm'
import { LoginFormPropsI } from './types'
import { CONFIG } from '@/constants/config'
import { cn } from '@/utils'

export function LoginForm({ isSignUp }: LoginFormPropsI) {
  const {
    formData,
    errors,
    isLoading,
    handleInputChange,
    handleSubmit,
    showPassword,
    togglePasswordVisibility
  } = useLoginForm(isSignUp)

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {isSignUp && (
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
            First name
          </label>
          <div className="mt-1">
            <input
              id="firstName"
              name="firstName"
              type="text"
              required
              value={formData.firstName}
              onChange={handleInputChange}
              className={cn(
                "appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
                errors.firstName 
                  ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
                  : "border-gray-300 focus:border-transparent",
                `focus:ring-[${CONFIG.colors.primary}]`
              )}
              placeholder="First name"
            />
            {errors.firstName && (
              <p className="mt-2 text-sm text-red-600">{errors.firstName}</p>
            )}
          </div>
        </div>
      )}

      {isSignUp && (
        <div>
          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
            Last name
          </label>
          <div className="mt-1">
            <input
              id="lastName"
              name="lastName"
              type="text"
              required
              value={formData.lastName}
              onChange={handleInputChange}
              className={cn(
                "appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
                errors.lastName 
                  ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
                  : "border-gray-300 focus:border-transparent",
                `focus:ring-[${CONFIG.colors.primary}]`
              )}
              placeholder="Last name"
            />
            {errors.lastName && (
              <p className="mt-2 text-sm text-red-600">{errors.lastName}</p>
            )}
          </div>
        </div>
      )}

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
          Email address
        </label>
        <div className="mt-1">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={formData.email}
            onChange={handleInputChange}
            className={cn(
              "appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
              errors.email 
                ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
                : "border-gray-300 focus:border-transparent",
              `focus:ring-[${CONFIG.colors.primary}]`
            )}
            placeholder="Email address"
          />
          {errors.email && (
            <p className="mt-2 text-sm text-red-600">{errors.email}</p>
          )}
        </div>
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
          Password
        </label>
        <div className="mt-1 relative">
          <input
            id="password"
            name="password"
            type={showPassword ? "text" : "password"}
            autoComplete={isSignUp ? "new-password" : "current-password"}
            required
            value={formData.password}
            onChange={handleInputChange}
            className={cn(
              "appearance-none block w-full px-3 py-2 pr-10 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
              errors.password 
                ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
                : "border-gray-300 focus:border-transparent",
              `focus:ring-[${CONFIG.colors.primary}]`
            )}
            placeholder="Password"
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={togglePasswordVisibility}
          >
            <span className="text-gray-400 text-sm">
              {showPassword ? 'Hide' : 'Show'}
            </span>
          </button>
          {errors.password && (
            <p className="mt-2 text-sm text-red-600">{errors.password}</p>
          )}
        </div>
      </div>

      {!isSignUp && (
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
              Remember me
            </label>
          </div>

          <div className="text-sm">
            <a href="#" className="font-medium hover:underline" style={{ color: CONFIG.colors.primary }}>
              Forgot your password?
            </a>
          </div>
        </div>
      )}

      <div>
        <button
          type="submit"
          disabled={isLoading}
          className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          style={{ 
            backgroundColor: CONFIG.colors.primary,
            focusRingColor: CONFIG.colors.primary
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = CONFIG.colors.primaryHover
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = CONFIG.colors.primary
          }}
        >
          {isLoading ? 'Loading...' : isSignUp ? 'Agree & Join' : 'Sign in'}
        </button>
      </div>

      {isSignUp && (
        <p className="text-xs text-gray-500 text-center">
          By clicking Agree & Join, you agree to the Network{' '}
          <a href="#" className="underline" style={{ color: CONFIG.colors.primary }}>
            User Agreement
          </a>
          ,{' '}
          <a href="#" className="underline" style={{ color: CONFIG.colors.primary }}>
            Privacy Policy
          </a>
          , and{' '}
          <a href="#" className="underline" style={{ color: CONFIG.colors.primary }}>
            Cookie Policy
          </a>
          .
        </p>
      )}
    </form>
  )
}
